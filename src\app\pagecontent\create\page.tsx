"use client";

import ImageUpload from '@/components/image/image-upload';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectItem,
} from '@/components/ui/select';
import {
    Tabs,
    TabsList,
    TabsTrigger,
    TabsContent,
} from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { PlusCircleIcon, XCircleIcon } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect, FormEvent } from 'react';

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), {
    ssr: false,
});

const initialCategories = [
    { name: 'Hiking', slug: 'hiking' },
    { name: 'Cooking', slug: 'cooking' },
    { name: 'Painting', slug: 'painting' },
];

const initialSubcategories = [
    { name: 'Mountain Hiking', slug: 'mountain-hiking', category: 'hiking' },
    { name: 'River Hiking', slug: 'river-hiking', category: 'hiking' },
    { name: 'Italian Cooking', slug: 'italian-cooking', category: 'cooking' },
    { name: 'French Cooking', slug: 'french-cooking', category: 'cooking' },
    { name: 'Portrait Painting', slug: 'portrait-painting', category: 'painting' },
];

const PageContentCreatePage: React.FC = () => {
    const [title, setTitle] = useState('');
    const [slug, setSlug] = useState('');
    const [description, setDescription] = useState('');
    const [category, setCategory] = useState(initialCategories[0].slug);
    const [subcategory, setSubcategory] = useState('')
    const [imageUrl, setImageUrl] = useState<string | null>(null);
    const [iconUrl, setIconUrl] = useState<string | null>(null);
    const [metaTitle, setMetaTitle] = useState('');
    const [metaDescription, setMetaDescription] = useState('');
    const [metaKeywords, setMetaKeywords] = useState('');
    const router = useRouter();

    const [contentBlocks, setContentBlocks] = useState([
        { title: '', description: '' },
    ]);

    const handleAddContentBlock = () => {
        setContentBlocks([...contentBlocks, { title: '', description: '' }]);
    };

    const handleContentChange = (
        index: number,
        field: 'title' | 'description',
        value: string
    ) => {
        const updated = [...contentBlocks];
        updated[index][field] = value;
        setContentBlocks(updated);
    };

    const handleRemoveContentBlock = (index: number) => {
        const updated = [...contentBlocks];
        updated.splice(index, 1);
        setContentBlocks(updated);
    };

    useEffect(() => {
        const filteredSubs = initialSubcategories.filter(sub => sub.category === category);
        setSubcategory(filteredSubs.length > 0 ? filteredSubs[0].slug : '');
    }, [category]);

    const filteredSubcategories = initialSubcategories.filter(sub => sub.category === category);

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        alert(`Created page content: ${title} (${slug}), category: ${category}, subcategory: ${subcategory}`);
        router.push('/pagecontent');
    };

    return (
        <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen ">
            <h1 className="text-2xl font-semibold mb-6 ">Create Page Content</h1>
            <form onSubmit={handleSubmit}>
                <Tabs defaultValue="basic-details" className="space-y-6">
                    <TabsList className="w-full flex space-x-6">
                        <TabsTrigger value="basic-details">Basic Details</TabsTrigger>
                        <TabsTrigger value="media">Media</TabsTrigger>
                        <TabsTrigger value="content">Content</TabsTrigger>
                        <TabsTrigger value="seo">SEO</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic-details" className="space-y-3">
                        <Card>
                            <CardContent className="space-y-3">
                                <div>
                                    <label htmlFor="title" className="block mb-2 font-medium text-gray-700">Title:</label>
                                    <Input
                                        id="title"
                                        type="text"
                                        value={title}
                                        onChange={e => setTitle(e.target.value)}
                                        required
                                        placeholder="Enter page title"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="slug" className="block mb-2 font-medium text-gray-700">Slug:</label>
                                    <Input
                                        id="slug"
                                        type="text"
                                        value={slug}
                                        onChange={e => setSlug(e.target.value)}
                                        required
                                        placeholder="Enter page slug"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="category" className="block mb-2 font-medium text-gray-700">Category:</label>
                                    <Select value={category} onValueChange={setCategory}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select a category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {initialCategories.map(cat => (
                                                <SelectItem key={cat.slug} value={cat.slug}>{cat.name}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <label htmlFor="subcategory" className="block mb-2 font-medium text-gray-700">Subcategory:</label>
                                    <Select value={subcategory} onValueChange={setSubcategory}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select a subcategory" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {filteredSubcategories.map(sub => (
                                                <SelectItem key={sub.slug} value={sub.slug}>{sub.name}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <label htmlFor="description" className="block mb-2 font-medium text-gray-700">Description:</label>
                                    <RichTextEditor
                                        value={description}
                                        onChange={value => setDescription(value)}
                                    />

                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="media" className="space-y-5">
                        <Card>
                            <CardContent className="space-y-3">

                                <div>
                                    <ImageUpload
                                        label="Image Upload"
                                        value={imageUrl}
                                        onChange={setImageUrl}
                                        accept="image/*"
                                    />
                                </div>
                                <div>
                                    <ImageUpload
                                        label="Icon Upload"
                                        value={iconUrl}
                                        onChange={setIconUrl}
                                        accept="image/*,image/svg+xml"
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="content" className="space-y-5 overflow-auto">
                        <Card>
                            <CardContent className="space-y-5">
                                {contentBlocks.map((block, idx) => (
                                    <div key={idx} className="border p-4 rounded relative">
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveContentBlock(idx)}
                                            className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                                            aria-label="Remove content block"
                                        >
                                            <XCircleIcon className="h-6 w-6" />
                                        </button>
                                        <div>
                                            <label className="block mb-1 font-medium">Title:</label>
                                            <Input
                                                type="text"
                                                value={block.title}
                                                onChange={e => handleContentChange(idx, 'title', e.target.value)}
                                                placeholder="Enter content title"
                                                className="w-full mb-3"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <label className="block mb-1 font-medium">Description:</label>
                                            <RichTextEditor
                                                value={block.description}
                                                onChange={value => handleContentChange(idx, 'description', value)}
                                            />
                                        </div>
                                    </div>
                                ))}

                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleAddContentBlock}
                                    className="flex items-center space-x-2"
                                >
                                    <PlusCircleIcon className="w-5 h-5" />
                                    <span>Add Content Block</span>
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="seo" className="space-y-5">
                        <Card>
                            <CardContent className="space-y-5">
                                <div>
                                    <label htmlFor="metaTitle" className="block mb-2 font-medium text-gray-700">Meta Title:</label>
                                    <Input
                                        id="metaTitle"
                                        type="text"
                                        value={metaTitle}
                                        onChange={e => setMetaTitle(e.target.value)}
                                        placeholder="Enter meta title for SEO"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="metaDescription" className="block mb-2 font-medium text-gray-700">Meta Description:</label>
                                    <Textarea
                                        id="metaDescription"
                                        value={metaDescription}
                                        onChange={e => setMetaDescription(e.target.value)}
                                        placeholder="Enter meta description for SEO"
                                        rows={3}
                                    />
                                </div>
                                <div>
                                    <label htmlFor="metaKeywords" className="block mb-2 font-medium text-gray-700">Meta Keywords:</label>
                                    <Input
                                        id="metaKeywords"
                                        type="text"
                                        value={metaKeywords}
                                        onChange={e => setMetaKeywords(e.target.value)}
                                        placeholder="Enter comma separated keywords"
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                </Tabs>
                <Button
                    type="submit"
                    className="mt-6 bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibold "
                >
                    Create
                </Button>
            </form>
        </div>
    );
};

export default PageContentCreatePage;
