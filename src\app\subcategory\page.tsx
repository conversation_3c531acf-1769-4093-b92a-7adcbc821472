"use client";

import React, { useState, useMemo } from 'react';
import Link from 'next/link';

type Subcategory = {
  name: string;
  slug: string;
  category: string;
};

const initialCategories = [
  { name: 'Hiking', slug: 'hiking' },
  { name: 'Cooking', slug: 'cooking' },
  { name: 'Painting', slug: 'painting' },
];

const initialSubcategories: Subcategory[] = [
  { name: 'Mountain Hiking', slug: 'mountain-hiking', category: 'hiking' },
  { name: 'Italian Cooking', slug: 'italian-cooking', category: 'cooking' },
];

const SubcategoryListPage: React.FC = () => {
  const [subcategories, setSubcategories] = useState<Subcategory[]>(initialSubcategories);
  const [searchTerm, setSearchTerm] = useState('');

  const handleDelete = (slug: string) => {
    if (confirm('Are you sure you want to delete this subcategory?')) {
      setSubcategories(subcategories.filter(sc => sc.slug !== slug));
    }
  };

  const filteredSubcategories = useMemo(() => {
    return subcategories.filter(sc =>
      sc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sc.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sc.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [subcategories, searchTerm]);

  const getCategoryName = (slug: string) => {
    const category = initialCategories.find(cat => cat.slug === slug);
    return category ? category.name : slug;
  };

  return (
    <div className="p-6 container mx-auto bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Subcategory List</h1>
        <Link
          href="/subcategory/create"
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
        >
          Create Subcategory
        </Link>
      </div>
      <div className="flex justify-between items-center mb-4">
        <div className="text-gray-700 font-medium">
          Total Subcategories: <span className="font-bold">{subcategories.length}</span>
        </div>
        <input
          type="text"
          placeholder="Search subcategories..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-60"
        />
      </div>
      <table className="w-full border border-gray-300 overflow-hidden shadow-sm">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700 w-12">
              SN
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Name
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Slug
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Category
            </th>
            <th className="px-6 py-3 border-b border-gray-300 text-left text-sm font-semibold text-gray-700">
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          {filteredSubcategories.length > 0 ? (
            filteredSubcategories.map((subcat, idx) => (
              <tr
                key={subcat.slug}
                className={`border-b border-gray-300 hover:bg-gray-50 cursor-pointer ${
                  idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                }`}
              >
                <td className="px-4 py-4 border-r border-gray-300">{idx + 1}</td>
                <td className="px-6 py-4 border-r border-gray-300">{subcat.name}</td>
                <td className="px-6 py-4 border-r border-gray-300">{subcat.slug}</td>
                <td className="px-6 py-4 border-r border-gray-300">{getCategoryName(subcat.category)}</td>
                <td className="px-6 py-4 flex gap-3 items-center">
                  <Link
                    href={`/subcategory/edit/${subcat.slug}`}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Edit
                  </Link>
                  <div className="h-6 border-l border-gray-300" aria-hidden="true"></div>
                  <button
                    onClick={() => handleDelete(subcat.slug)}
                    className="text-red-600 hover:text-red-800 font-medium"
                    type="button"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={5} className="text-center py-4 text-gray-500">
                No subcategories found.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default SubcategoryListPage;
