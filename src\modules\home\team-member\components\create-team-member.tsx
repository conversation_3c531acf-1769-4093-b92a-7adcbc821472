'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import ImageUpload from '@/components/image/image-upload';

interface TeamMember {
  id: string;
  imageUrl: string | null;
  name: string;
  role: string;
}

const TeamMemberCreatePage: React.FC = () => {
  const [mainTitle, setMainTitle] = useState('');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  const handleAddMember = () => {
    const newMember: TeamMember = {
      id: (teamMembers.length + 1 + Math.random()).toString(),
      imageUrl: null,
      name: '',
      role: '',
    };
    setTeamMembers((prev) => [...prev, newMember]);
  };

  const handleRemoveMember = (id: string) => {
    setTeamMembers((prev) => prev.filter((m) => m.id !== id));
  };

  const handleMemberChange = (
    id: string,
    field: keyof Omit<TeamMember, 'id'>,
    value: string | null
  ) => {
    setTeamMembers((prev) =>
      prev.map((member) =>
        member.id === id ? { ...member, [field]: value } : member
      )
    );
  };

  const handleCreate = () => {
    if (!mainTitle.trim()) {
      alert('Please enter the main title');
      return;
    }
    if (teamMembers.length === 0) {
      alert('Please add at least one team member');
      return;
    }
    for (const member of teamMembers) {
      if (!member.imageUrl) {
        alert('All team members must have an image');
        return;
      }
      if (!member.name.trim()) {
        alert('All team members must have a name');
        return;
      }
      if (!member.role.trim()) {
        alert('All team members must have a role');
        return;
      }
    }
    alert('Team member section created (dummy)');
    setMainTitle('');
    setTeamMembers([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-6">Create Team Member Section</h2>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Main Title</label>
        <input
          type="text"
          value={mainTitle}
          onChange={(e) => setMainTitle(e.target.value)}
          placeholder="Enter main title"
          className="w-full border rounded px-3 py-2"
        />
      </div>

      <Button onClick={handleAddMember} className="mb-6 px-4 py-2">
        Add Team Member
      </Button>

      {teamMembers.map((member, idx) => (
        <div
          key={member.id}
          className="mb-8 p-4 border rounded bg-white shadow-sm relative"
        >
          <button
            type="button"
            onClick={() => handleRemoveMember(member.id)}
            className="absolute top-2 right-2 text-red-600 hover:bg-red-100 rounded px-2"
            aria-label={`Remove member ${idx + 1}`}
          >
            ✕
          </button>

          <div className="mb-4">
            <ImageUpload
              label={`Member ${idx + 1} Image`}
              value={member.imageUrl}
              onChange={(url) => handleMemberChange(member.id, 'imageUrl', url)}
            />
          </div>

          <div className="mb-4">
            <label className="block mb-1 font-medium">Name</label>
            <input
              type="text"
              value={member.name}
              onChange={(e) =>
                handleMemberChange(member.id, 'name', e.target.value)
              }
              placeholder="Enter name"
              className="w-full border rounded px-3 py-2"
            />
          </div>

          <div className="mb-4">
            <label className="block mb-1 font-medium">Role</label>
            <input
              type="text"
              value={member.role}
              onChange={(e) =>
                handleMemberChange(member.id, 'role', e.target.value)
              }
              placeholder="Enter role"
              className="w-full border rounded px-3 py-2"
            />
          </div>
        </div>
      ))}

      <div className="flex gap-4">
        <Button
          onClick={handleCreate}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Create
        </Button>
        {/* Add Cancel button if needed */}
      </div>
    </div>
  );
};

export default TeamMemberCreatePage;
