'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import ImageUpload from '@/components/image/image-upload';

interface TeamMember {
  id: string;
  imageUrl: string | null;
  name: string;
  role: string;
}

const initialMembers: TeamMember[] = [
  {
    id: '1',
    imageUrl: '/images/member1.webp',
    name: '<PERSON>',
    role: 'CEO',
  },
  {
    id: '2',
    imageUrl: '/images/member2.webp',
    name: '<PERSON>',
    role: 'CTO',
  },
];

const TeamMemberEditPage: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const id = pathname?.split('/').pop() || '';

  const [member, setMember] = useState<TeamMember | null>(null);

  useEffect(() => {
    const found = initialMembers.find((m) => m.id === id);
    setMember(found ?? null);
  }, [id]);

  const handleChange = (field: keyof Omit<TeamMember, 'id' | 'imageUrl'>, value: string) => {
    if (!member) return;
    setMember({ ...member, [field]: value });
  };

  const handleImageChange = (imageUrl: string | null) => {
    if (!member) return;
    setMember({ ...member, imageUrl });
  };

  const handleUpdate = () => {
    if (!member) return;

    if (!member.name.trim()) {
      alert('Name is required');
      return;
    }
    if (!member.role.trim()) {
      alert('Role is required');
      return;
    }
    if (!member.imageUrl) {
      alert('Image is required');
      return;
    }

    alert('Team member updated (dummy)');
    router.push('/home/<USER>');
  };

  if (!member) {
    return (
      <div className="p-6 max-w-md mx-auto">
        <p>Team member not found.</p>
        <Button onClick={() => router.push('/home/<USER>')}>
          Back to List
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-md mx-auto bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-6">Edit Team Member</h2>

      <div className="mb-6">
        <ImageUpload
          label="Team Member Image"
          value={member.imageUrl}
          onChange={handleImageChange}
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Name</label>
        <input
          type="text"
          value={member.name}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="Enter name"
          className="w-full border rounded px-3 py-2"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Role</label>
        <input
          type="text"
          value={member.role}
          onChange={(e) => handleChange('role', e.target.value)}
          placeholder="Enter role"
          className="w-full border rounded px-3 py-2"
        />
      </div>

      <div className="flex gap-4">
        <Button
          onClick={handleUpdate}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Update
        </Button>
        <Button
          variant="secondary"
          onClick={() => router.push('/home/<USER>')}
          className="px-6 py-2"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TeamMemberEditPage;
