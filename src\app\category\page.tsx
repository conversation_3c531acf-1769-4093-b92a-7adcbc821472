"use client";

import React, { useMemo, useState } from 'react';
import Link from 'next/link';
import { useGetCategory } from '@/modules/category/queries/list-category';
import { useDeleteCategory } from '@/modules/category/mutations/delete-category';

const CategoryPage: React.FC = () => {
  const { data, isLoading, isError } = useGetCategory();
  const deleteCategoryMutation = useDeleteCategory();
  const [searchTerm, setSearchTerm] = useState<string>('');

  const categories = data?.data ?? [];

  const filteredCategories = useMemo(() => {
    return categories.filter(
      (cat) =>
        cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cat.slug.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [categories, searchTerm]);

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this category?')) {
      deleteCategoryMutation.mutate(id);
    }
  };

  return (
    <div className="p-6 container mx-auto bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Category List</h1>
        <Link
          href="/category/create"
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
        >
          Create Category
        </Link>
      </div>

      <div className="flex justify-between items-center mb-4">
        <div className="text-gray-700 font-medium">
          Total Categories: <span className="font-bold">{categories.length}</span>
        </div>
        <input
          type="text"
          placeholder="Search categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-60"
        />
      </div>

      {isLoading && (
        <div className="text-center py-8 text-gray-500">Loading categories...</div>
      )}

      {isError && (
        <div className="text-center py-8 text-red-600">Failed to load categories.</div>
      )}

      {!isLoading && !isError && (
        <table className="w-full border border-gray-300 overflow-hidden shadow-sm">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-4 py-3 border-b border-gray-300 border-r  text-left text-sm font-semibold text-gray-700 w-12">SN</th>
              <th className="px-6 py-3 border-b border-gray-300 border-r text-left text-sm font-semibold text-gray-700">Name</th>
              <th className="px-6 py-3 border-b border-gray-300 border-r text-left text-sm font-semibold text-gray-700">Slug</th>
              <th className="px-6 py-3 border-b border-gray-300 text-left text-sm font-semibold text-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredCategories.length > 0 ? (
              filteredCategories.map((cat, idx) => (
                <tr
                  key={cat.slug}
                  className={`border-b border-gray-300 hover:bg-gray-50 cursor-pointer ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <td className="px-4 py-4 border-r border-gray-300">{idx + 1}</td>
                  <td className="px-6 py-4 border-r border-gray-300">{cat.name}</td>
                  <td className="px-6 py-4 border-r border-gray-300">{cat.slug}</td>
                  <td className="px-6 py-4 flex gap-3 items-center">
                    <Link
                      href={`/category/edit/${cat.slug}`}
                      className="text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Edit
                    </Link>
                    <div className="h-6 border-l border-gray-300" aria-hidden="true"></div>
                    <button
                      onClick={() => handleDelete(cat.id)}
                      className="text-red-600 hover:text-red-800 font-medium"
                      type="button"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4} className="text-center py-4 text-gray-500">
                  No categories found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default CategoryPage;
