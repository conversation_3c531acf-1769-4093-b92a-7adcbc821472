"use client";

import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useRouter, useParams } from 'next/navigation';
import React, { useState, useEffect, FormEvent } from 'react';

const initialCategories = [
    { name: 'Hiking', slug: 'hiking' },
    { name: 'Cooking', slug: 'cooking' },
    { name: 'Painting', slug: 'painting' },
];

const initialSubcategories = [
    { name: 'Mountain Hiking', slug: 'mountain-hiking', category: 'hiking' },
    { name: 'Italian Cooking', slug: 'italian-cooking', category: 'cooking' },
];

const SubcategoryEditPage: React.FC = () => {
    const router = useRouter();
    const params = useParams();
    const slug = params.slug;

    const [name, setName] = useState('');
    const [newSlug, setNewSlug] = useState('');
    const [category, setCategory] = useState(initialCategories[0].slug);

    useEffect(() => {
        if (typeof slug === 'string') {
            const subcategory = initialSubcategories.find(sc => sc.slug === slug);
            if (subcategory) {
                setName(subcategory.name);
                setNewSlug(subcategory.slug);
                setCategory(subcategory.category);
            }
        }
    }, [slug]);

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        alert(`Updated subcategory: ${name} with slug: ${newSlug} under category: ${category}`);
        // Add API update logic here
        router.push('/subcategory');
    };

    return (
        <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen ">
            <h1 className="text-2xl font-semibold mb-6 ">Edit Subcategory</h1>
            <form onSubmit={handleSubmit} className="space-y-5">
                <div>
                    <label htmlFor="name" className="block mb-2 font-medium text-gray-700">
                        Name:
                    </label>
                    <input
                        id="name"
                        type="text"
                        value={name}
                        onChange={e => setName(e.target.value)}
                        required
                        placeholder="Enter subcategory name"
                        className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                </div>
                <div>
                    <label htmlFor="slug" className="block mb-2 font-medium text-gray-700">
                        Slug:
                    </label>
                    <input
                        id="slug"
                        type="text"
                        value={newSlug}
                        onChange={e => setNewSlug(e.target.value)}
                        required
                        placeholder="Enter subcategory slug"
                        className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                </div>
                <div>
                    <label htmlFor="category" className="block mb-2 font-medium text-gray-700">
                        Category:
                    </label>
                    <Select value={category} onValueChange={setCategory}>
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                            {initialCategories.map(cat => (
                                <SelectItem key={cat.slug} value={cat.slug}>
                                    {cat.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <Button
                    type="submit"
                    className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibol"
                >
                    Save
                </Button>
            </form>
        </div>
    );
};

export default SubcategoryEditPage;
