"use client";

import { Button } from '@/components/ui/button';
import { useUpdateCategory } from '@/modules/category/mutations/update-category';
import { useGetCategory } from '@/modules/category/queries/list-category';
import { useRouter, useParams } from 'next/navigation';
import React, { useState, useEffect, FormEvent } from 'react';

const EditCategoryPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const slug = typeof params.slug === 'string' ? params.slug : '';

  const { data, isLoading, isError } = useGetCategory();
  const updateCategoryMutation = useUpdateCategory();

  // State for form inputs
  const [name, setName] = useState('');
  const [newSlug, setNewSlug] = useState('');
  const [categoryId, setCategoryId] = useState<string | null>(null); // store id for update

  // When data loads or slug changes, find category and populate form
  useEffect(() => {
    if (data?.data && slug) {
      const category = data.data.find(cat => cat.slug === slug);
      if (category) {
        setName(category.name);
        setNewSlug(category.slug);
        setCategoryId(category.id);
      }
    }
  }, [data, slug]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!categoryId) return;

    updateCategoryMutation.mutate(
      { id: categoryId, name, slug: newSlug },
      {
        onSuccess: () => router.push('/category'),
      }
    );
  };

  if (isLoading) return <div className="p-6">Loading category...</div>;
  if (isError || !categoryId)
    return <div className="p-6 text-red-600">Category not found</div>;

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen ">
      <h1 className="text-2xl font-semibold mb-6">Edit Category</h1>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label htmlFor="name" className="block mb-2 font-medium text-gray-700">
            Name:
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={e => setName(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter category name"
          />
        </div>
        <div>
          <label htmlFor="slug" className="block mb-2 font-medium text-gray-700">
            Slug:
          </label>
          <input
            id="slug"
            type="text"
            value={newSlug}
            onChange={e => setNewSlug(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter category slug"
          />
        </div>
        <Button
          type="submit"
          className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibold"
        >
          Save
        </Button>
      </form>
    </div>
  );
};

export default EditCategoryPage;
