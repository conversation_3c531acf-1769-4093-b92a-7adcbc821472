"use client";

import React, { useState, useMemo } from 'react';
import Link from 'next/link';

type PageContent = {
  title: string;
  slug: string;
  category: string;
  subcategory: string;
};

const initialCategories = [
  { name: 'Hiking', slug: 'hiking' },
  { name: 'Cooking', slug: 'cooking' },
  { name: 'Painting', slug: 'painting' },
];

const initialSubcategories = [
  { name: 'Mountain Hiking', slug: 'mountain-hiking', category: 'hiking' },
  { name: 'Italian Cooking', slug: 'italian-cooking', category: 'cooking' },
];

const initialPageContents: PageContent[] = [
  {
    title: 'Best Mountain Trails',
    slug: 'best-mountain-trails',
    category: 'hiking',
    subcategory: 'mountain-hiking',
  },
  {
    title: 'How to Cook Pasta',
    slug: 'how-to-cook-pasta',
    category: 'cooking',
    subcategory: 'italian-cooking',
  },
];

const PageContentListPage: React.FC = () => {
  const [pageContents, setPageContents] = useState<PageContent[]>(initialPageContents);
  const [searchTerm, setSearchTerm] = useState('');

  const handleDelete = (slug: string) => {
    if (confirm('Are you sure you want to delete this page content?')) {
      setPageContents(pageContents.filter(pc => pc.slug !== slug));
    }
  };

  const filteredPageContents = useMemo(() => {
    return pageContents.filter(pc =>
      pc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pc.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pc.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pc.subcategory.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [pageContents, searchTerm]);

  const getCategoryName = (slug: string) => {
    const category = initialCategories.find(cat => cat.slug === slug);
    return category ? category.name : slug;
  };

  const getSubcategoryName = (slug: string) => {
    const subcategory = initialSubcategories.find(sub => sub.slug === slug);
    return subcategory ? subcategory.name : slug;
  };

  return (
    <div className="p-6 container mx-auto bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Page Content List</h1>
        <Link
          href="/pagecontent/create"
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
        >
          Create Page Content
        </Link>
      </div>
      <div className="flex justify-between items-center mb-4">
        <div className="text-gray-700 font-medium">
          Total Page Contents: <span className="font-bold">{pageContents.length}</span>
        </div>
        <input
          type="text"
          placeholder="Search page contents..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-72"
        />
      </div>
      <table className="w-full border border-gray-300 overflow-hidden shadow-sm">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700 w-12">
              SN
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Title
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Slug
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Category
            </th>
            <th className="px-6 py-3 border-b border-r border-gray-300 text-left text-sm font-semibold text-gray-700">
              Subcategory
            </th>
            <th className="px-6 py-3 border-b border-gray-300 text-left text-sm font-semibold text-gray-700">
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          {filteredPageContents.length > 0 ? (
            filteredPageContents.map((pc, idx) => (
              <tr
                key={pc.slug}
                className={`border-b border-gray-300 hover:bg-gray-50 cursor-pointer ${
                  idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                }`}
              >
                <td className="px-4 py-4 border-r border-gray-300">{idx + 1}</td>
                <td className="px-6 py-4 border-r border-gray-300">{pc.title}</td>
                <td className="px-6 py-4 border-r border-gray-300">{pc.slug}</td>
                <td className="px-6 py-4 border-r border-gray-300">{getCategoryName(pc.category)}</td>
                <td className="px-6 py-4 border-r border-gray-300">{getSubcategoryName(pc.subcategory)}</td>
                <td className="px-6 py-4 flex gap-3 items-center">
                  <Link
                    href={`/pagecontent/edit/${pc.slug}`}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Edit
                  </Link>
                  <div className="h-6 border-l border-gray-300" aria-hidden="true"></div>
                  <button
                    onClick={() => handleDelete(pc.slug)}
                    className="text-red-600 hover:text-red-800 font-medium"
                    type="button"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={6} className="text-center py-4 text-gray-500">
                No page contents found.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default PageContentListPage;
