'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import ImageUpload from '@/components/image/image-upload';

interface SupportSectionData {
    id: string;
    imageUrl: string | null;
    title: string;
    subtitle: string;
    buttonLabel: string;
    buttonUrl: string;
}

const initialData: SupportSectionData[] = [
    {
        id: '1',
        imageUrl: '/images/image2.webp',
        title: '24/7 Support',
        subtitle: 'We are here whenever you need help.',
        buttonLabel: 'Contact Us',
        buttonUrl: 'https://example.com/contact',
    },
];

const SupportSectionEditPage: React.FC = () => {
    const router = useRouter();
    const pathname = usePathname();

    const [data, setData] = useState<SupportSectionData | null>(null);

    useEffect(() => {
        const found = initialData[0];
        setData(found ?? null);
    }, []);

    const handleChange = (
        field: keyof Omit<SupportSectionData, 'id' | 'imageUrl'>,
        value: string
    ) => {
        if (!data) return;
        setData({ ...data, [field]: value });
    };

    const handleImageChange = (imageUrl: string | null) => {
        if (!data) return;
        setData({ ...data, imageUrl });
    };

    const handleUpdate = () => {
        if (!data) return;
        if (!data.title.trim()) {
            alert('Title is required');
            return;
        }
        if (!data.subtitle.trim()) {
            alert('Subtitle is required');
            return;
        }
        if (!data.buttonLabel.trim()) {
            alert('Button label is required');
            return;
        }
        if (!data.buttonUrl.trim()) {
            alert('Button URL is required');
            return;
        }
        try {
            new URL(data.buttonUrl);
        } catch {
            alert('Button URL is invalid');
            return;
        }
        alert('Support section updated (dummy)');
        router.push('/home');
    };

    if (!data) {
        return (
            <div className="p-6 max-w-md mx-auto">
                <p>Support item not found.</p>
                <Button onClick={() => router.push('/home')}>
                    Back to List
                </Button>
            </div>
        );
    }

    return (
        <div className="p-6 container mx-auto bg-gray-50 min-h-screen">
            <h2 className="text-2xl font-semibold mb-6">Edit Support Item</h2>

            <div className="mb-4">
                <ImageUpload
                    label="Support Image"
                    value={data.imageUrl}
                    onChange={handleImageChange}
                />
            </div>

            <div className="mb-4">
                <label className="block mb-1 font-medium">Title</label>
                <input
                    type="text"
                    value={data.title}
                    onChange={(e) => handleChange('title', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="Enter title"
                />
            </div>

            <div className="mb-4">
                <label className="block mb-1 font-medium">Subtitle</label>
                <input
                    type="text"
                    value={data.subtitle}
                    onChange={(e) => handleChange('subtitle', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="Enter subtitle"
                />
            </div>

            <div className="mb-4">
                <label className="block mb-1 font-medium">Button Label</label>
                <input
                    type="text"
                    value={data.buttonLabel}
                    onChange={(e) => handleChange('buttonLabel', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="Enter button label"
                />
            </div>

            <div className="mb-6">
                <label className="block mb-1 font-medium">Button URL</label>
                <input
                    type="url"
                    value={data.buttonUrl}
                    onChange={(e) => handleChange('buttonUrl', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="https://example.com"
                />
            </div>

            <div className="flex gap-4">
                <Button
                    onClick={handleUpdate}
                    className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
                >
                    Update
                </Button>
                <Button
                    variant="secondary"
                    onClick={() => router.push('/home')}
                    className="px-6 py-2"
                >
                    Cancel
                </Button>
            </div>
        </div>
    );
};

export default SupportSectionEditPage;
